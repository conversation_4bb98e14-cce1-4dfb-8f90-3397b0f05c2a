import React, { useState, useMemo } from 'react';
import { Dropdown, Tooltip, Modal } from 'antd';
import { MiniApp } from '../../types/miniapp';
import { Archive, Trash2, Play } from 'lucide-react';
import archiveIcon from '~/assets/icons/archive.svg';
import dotIcon from '~/assets/icons/dot.svg';
import newminiappIcon from '~/assets/icons/newminiapp.svg';

interface MiniappApplicationsListProps {
  miniapps: MiniApp[];
  onSelectMiniapp: (miniapp: MiniApp) => void;
  onNewProject: () => void;
  onArchiveMiniapp?: (miniapp: MiniApp) => void;
  onDeleteMiniapp?: (miniapp: MiniApp) => void;
  onActivateMiniapp?: (miniapp: MiniApp) => void;
  onShowArchived?: () => void;
  isArchivedView?: boolean;
}

type FilterType = 'All' | 'Deployed' | 'Developing';

const MiniappApplicationsList: React.FC<MiniappApplicationsListProps> = ({
  miniapps,
  onSelectMiniapp,
  onNewProject,
  onArchiveMiniapp,
  onDeleteMiniapp,
  onActivateMiniapp,
  onShowArchived,
  isArchivedView = false,
}) => {
  const [filter, setFilter] = useState<FilterType>('All');
  const [showArchiveDialog, setShowArchiveDialog] = useState(false);
  const [miniappToArchive, setMiniappToArchive] = useState<MiniApp | null>(null);

  // Generate avatar colors
  const getAvatarColor = (name: string) => {
    const colors = [
      '#ef4444',
      '#f97316',
      '#f59e0b',
      '#eab308',
      '#84cc16',
      '#22c55e',
      '#10b981',
      '#14b8a6',
      '#06b6d4',
      '#0ea5e9',
      '#3b82f6',
      '#6366f1',
      '#8b5cf6',
      '#a855f7',
      '#d946ef',
      '#ec4899',
      '#f43f5e',
    ];
    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  };

  // Get initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('');
  };

  // Get status text
  const getStatus = (miniapp: MiniApp) => {
    if (miniapp.installation) {
      return 'Deployed';
    }
    return 'Developing';
  };

  // Filter miniapps
  const filteredMiniapps = useMemo(() => {
    if (filter === 'All') return miniapps;
    if (filter === 'Deployed') return miniapps.filter(app => app.installation);
    if (filter === 'Developing') return miniapps.filter(app => !app.installation);
    return miniapps;
  }, [miniapps, filter]);

  // Filter dropdown items
  const filterItems = [
    { key: 'All', label: 'All' },
    { key: 'Deployed', label: 'Deployed' },
    { key: 'Developing', label: 'Developing' },
  ];

  // Handle archive confirmation
  const handleArchiveClick = (miniapp: MiniApp) => {
    console.debug('---handleArchiveClick miniapp', miniapp);
    setMiniappToArchive(miniapp);
    setShowArchiveDialog(true);
  };

  const handleArchiveConfirm = () => {
    if (miniappToArchive && onArchiveMiniapp) {
      onArchiveMiniapp(miniappToArchive);
    }
    setShowArchiveDialog(false);
    setMiniappToArchive(null);
  };

  const handleArchiveCancel = () => {
    setShowArchiveDialog(false);
    setMiniappToArchive(null);
  };

  // Dot menu items
  const getDotMenuItems = (miniapp: MiniApp) => {
    if (isArchivedView) {
      return [
        {
          key: 'activate',
          label: (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Play size={16} />
              Activate
            </div>
          ),
          onClick: ({ domEvent }: any) => {
            domEvent?.stopPropagation();
            onActivateMiniapp?.(miniapp);
          },
        },
        {
          key: 'delete',
          label: (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Trash2 size={16} />
              Delete
            </div>
          ),
          onClick: ({ domEvent }: any) => {
            domEvent?.stopPropagation();
            onDeleteMiniapp?.(miniapp);
          },
        },
      ];
    } else {
      return [
        {
          key: 'archive',
          label: (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Archive size={16} />
              Archive
            </div>
          ),
          onClick: ({ domEvent }: any) => {
            domEvent?.stopPropagation();
            handleArchiveClick(miniapp);
          },
        },
        {
          key: 'delete',
          label: (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Trash2 size={16} />
              Delete
            </div>
          ),
          onClick: ({ domEvent }: any) => {
            domEvent?.stopPropagation();
            onDeleteMiniapp?.(miniapp);
          },
        },
      ];
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      {/* Pin top bar */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '12px 16px',
          borderBottom: '1px solid #e5e7eb',
          backgroundColor: '#ffffff',
        }}
      >
        {/* Filter dropdown */}
        <Dropdown
          menu={{
            items: filterItems,
            onClick: ({ key }) => setFilter(key as FilterType),
          }}
          trigger={['click']}
        >
          <button
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 12px',
              borderRadius: '6px',
              border: '1px solid #d1d5db',
              backgroundColor: '#ffffff',
              color: '#374151',
              fontSize: '14px',
              cursor: 'pointer',
              transition: 'all 0.2s',
            }}
          >
            {filter}
            <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
              <path d="M3 4.5L6 7.5L9 4.5" stroke="currentColor" strokeWidth="1.5" fill="none" />
            </svg>
          </button>
        </Dropdown>

        {/* Archived button - only show in non-archived view */}
        {!isArchivedView && (
          <Tooltip title="Archived">
            <button
              onClick={onShowArchived}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '6px',
                border: 'none',
                backgroundColor: 'transparent',
                cursor: 'pointer',
                transition: 'background 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#f3f4f6';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <img src={archiveIcon} alt="Archive" style={{ width: 18, height: 18 }} />
            </button>
          </Tooltip>
        )}
      </div>

      {/* Miniapp list */}
      <div
        style={{
          flex: 1,
          overflowY: 'auto',
          padding: '16px',
          backgroundColor: '#f9fafb',
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          {filteredMiniapps.map(miniapp => (
            <div
              key={miniapp.id}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                padding: '12px',
                borderRadius: '8px',
                backgroundColor: '#ffffff',
                border: '1px solid #e5e7eb',
                cursor: 'pointer',
                transition: 'all 0.2s',
              }}
              onClick={() => onSelectMiniapp(miniapp)}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#f8fafc';
                e.currentTarget.style.borderColor = '#d1d5db';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = '#ffffff';
                e.currentTarget.style.borderColor = '#e5e7eb';
              }}
            >
              {/* Avatar */}
              <div
                style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '8px',
                  backgroundColor: getAvatarColor(miniapp.name),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#ffffff',
                  fontSize: '14px',
                  fontWeight: 600,
                  flexShrink: 0,
                }}
              >
                {getInitials(miniapp.name)}
              </div>

              {/* Name and status */}
              <div style={{ flex: 1, minWidth: 0 }}>
                <div
                  style={{
                    fontSize: '14px',
                    fontWeight: 500,
                    color: '#111827',
                    marginBottom: '2px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {miniapp.name}
                </div>
                <div
                  style={{
                    fontSize: '12px',
                    color: '#6b7280',
                  }}
                >
                  {getStatus(miniapp)}
                </div>
              </div>

              {/* Dot menu */}
              <div onClick={e => e.stopPropagation()}>
                <Dropdown
                  menu={{ items: getDotMenuItems(miniapp) }}
                  trigger={['click']}
                  placement="bottomRight"
                >
                  <button
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '24px',
                      height: '24px',
                      borderRadius: '4px',
                      border: 'none',
                      backgroundColor: 'transparent',
                      cursor: 'pointer',
                      transition: 'background 0.2s',
                    }}
                    onClick={e => e.stopPropagation()}
                    onMouseOver={e => {
                      e.currentTarget.style.backgroundColor = '#f3f4f6';
                    }}
                    onMouseOut={e => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                  >
                    <img src={dotIcon} alt="More" style={{ width: 16, height: 16 }} />
                  </button>
                </Dropdown>
              </div>
            </div>
          ))}
        </div>

        {/* New Project button - only show in non-archived view */}
        {!isArchivedView && (
          <button
            onClick={onNewProject}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              width: '100%',
              padding: '12px 24px',
              marginTop: '16px',
              borderRadius: '8px',
              border: '1px solid #d1d5db',
              backgroundColor: '#ffffff',
              color: '#374151',
              fontSize: '14px',
              fontWeight: 500,
              cursor: 'pointer',
              transition: 'all 0.2s',
            }}
            onMouseOver={e => {
              e.currentTarget.style.backgroundColor = '#f9fafb';
              e.currentTarget.style.borderColor = '#9ca3af';
            }}
            onMouseOut={e => {
              e.currentTarget.style.backgroundColor = '#ffffff';
              e.currentTarget.style.borderColor = '#d1d5db';
            }}
          >
            <img src={newminiappIcon} alt="New Project" style={{ width: 20, height: 20 }} />
            New Project
          </button>
        )}
      </div>

      {/* Archive Confirmation Dialog */}
      <Modal
        title={
          <div style={{ textAlign: 'center', fontSize: '20px', fontWeight: 600, color: '#111827' }}>
            Archiving
          </div>
        }
        open={showArchiveDialog}
        onCancel={handleArchiveCancel}
        footer={null}
        centered
        width={300}
        closable={false}
      >
        <div style={{ textAlign: 'center' }}>
          <p
            style={{
              fontSize: '14px',
              color: '#374151',
              lineHeight: '1.6',
              fontWeight: 400,
            }}
          >
            You are about to archive the MiniAPP.
            <br />
            After archiving, it needs to be activated
            <br />
            before you can use it.
          </p>

          {/* Custom Buttons */}
          <div style={{ display: 'flex', gap: '16px', justifyContent: 'center' }}>
            <button
              onClick={handleArchiveCancel}
              style={{
                padding: '12px 32px',
                borderRadius: '8px',
                border: '1px solid #d1d5db',
                backgroundColor: '#ffffff',
                color: '#374151',
                fontSize: '16px',
                fontWeight: 500,
                cursor: 'pointer',
                minWidth: '100px',
                transition: 'all 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
                e.currentTarget.style.borderColor = '#9ca3af';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = '#ffffff';
                e.currentTarget.style.borderColor = '#d1d5db';
              }}
            >
              No
            </button>
            <button
              onClick={handleArchiveConfirm}
              style={{
                padding: '12px 32px',
                borderRadius: '8px',
                border: 'none',
                backgroundColor: '#374151',
                color: '#ffffff',
                fontSize: '16px',
                fontWeight: 500,
                cursor: 'pointer',
                minWidth: '100px',
                transition: 'all 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#1f2937';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = '#374151';
              }}
            >
              Yes
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default MiniappApplicationsList;
